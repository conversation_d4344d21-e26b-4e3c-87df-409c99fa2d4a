from fastapi import APIRouter
from bots.types import BotParams
from fastapi.responses import StreamingResponse

router = APIRouter(prefix="/bot")


@router.post("/action", response_class=StreamingResponse)
async def stream_action(
    params: BotParams,
) -> StreamingResponse:
    """
    Single-turn HTTP action endpoint.

    This endpoint initiates a streaming response and returns chunks of the bot's
    response in real-time using server-sent events.

    Args:
        params (BotParams): Parameters for the bot interaction, must include:
            - conversation_id: UUID of the conversation to process
            - attachments: List of attachment IDs to include in the bot's response

    Returns:
        StreamingResponse: A streaming response with media type "text/event-stream"
            containing the bot's response chunks.

    Raises:
        HTTPException (400): When conversation_id is missing from params.
        HTTPException (404): When the specified conversation is not found.
        HTTPException (400): When service validation fails (via _validate_services).

    Flow:
        1. Validates the presence of conversation_id
        2. Checks if the conversation exists in the database
        3. Retrieves conversation history
        4. Validates bot services configuration
        5. Runs the bot pipeline and streams the response
    """

    print(params)

    return StreamingResponse("world", media_type="text/event-stream")
